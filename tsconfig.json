{"ts-node": {"compilerOptions": {"module": "commonjs"}}, "compilerOptions": {"types": ["node", "@testing-library/jest-dom", "vitest/globals"], "jsx": "react-jsx", "target": "ESNext", "module": "ESNext", "lib": ["dom", "ESNext"], "experimentalDecorators": true, "declaration": true, "forceConsistentCasingInFileNames": true, "allowJs": false, "skipLibCheck": true, "strictNullChecks": true, "esModuleInterop": true, "noImplicitAny": true, "sourceMap": true, "strict": true, "baseUrl": ".", "outDir": "dist", "moduleResolution": "node", "resolveJsonModule": true, "noErrorTruncation": true, "plugins": [{"name": "typescript-plugin-css-modules"}], "paths": {"@/*": ["./src/*"]}}, "include": ["src/**/*", "*.ts", "*.tsx", "*.module.css"], "exclude": ["node_modules", "dist", "out"]}