export {};

declare global {
  declare module "*.module.css" {
    const classes: Readonly<Record<string, string>>;
    export default classes;
  }

  var prismaId: number;
  var prismaPool: import("@prisma/client").PrismaClient[];

  interface Window {
    electronWindow: typeof import("@/helpers/ipc/window/window-context").windowContext;
    electronPrisma: typeof import("@/helpers/ipc/prisma/prisma-context").prismaContext;
    electronDevtools: typeof import("@/helpers/ipc/devtools/devtools-context").devtoolsContext;
  }
}
