import LocalGallerySyncModal from "@/components/local-gallery-sync/LocalGallerySyncModal";
import ImageCard from "@/components/local-gallery/image-card/ImageCard";
import { LocalGalleryFooter } from "@/components/local-gallery/local-gallery-footer/LocalGalleryFooter";
import LocalImageModal from "@/components/local-image/LocalImageModal";
import ImageGrid from "@/components/shared/image-grid/ImageGrid";
import LoaderModal from "@/components/shared/loader-modal/LoaderModal";
import { FetchStatus } from "@/helpers/constants";
import type { LocalImage } from "@/helpers/ipc/prisma/local-images/types";
import { useAppDispatch, useAppSelector } from "@/hooks";
import { useImageHotkeys } from "@/hooks/local-gallery/useImageHotkeys";
import { useImageRefs } from "@/hooks/local-gallery/useImageRefs";
import PageLayout from "@/layouts/PageLayout";
import {
  archive,
  deleteLocalImages,
  fetchLocalImages,
  openImage,
  select,
  selectLocalGallery,
  unarchive,
  unselect,
} from "@/store/local-gallery-slice";
import { useHotkeys } from "@mantine/hooks";
import { useCallback, useEffect } from "react";

export default function LocalGallery() {
  const dispatch = useAppDispatch();
  const { refs, setRef, setFocus } = useImageRefs();
  const { getImageCardHotkeys } = useImageHotkeys(refs);

  const {
    fetch,
    localImage,
    localImages,
    currentPage,
    selectedIds,
    pagination,
    statistics: { pages },
  } = useAppSelector(selectLocalGallery);
  console.log("sdfsdfsd");
  // If there are no local images, fetch them
  useEffect(() => {
    if (fetch.status === FetchStatus.IDLE) {
      dispatch(fetchLocalImages()).then(() => setFocus(0));
    }
  }, [fetch.status]);

  // If current page exceeds the total pages, reset to the last page
  useEffect(() => {
    if (currentPage > pages) {
      setPage(pages);
    }
  }, [deleteLocalImages, currentPage, pages]);

  const onOpenImage = (id: number) => {
    window.electronPrisma
      .loadLocalImage({ ...pagination, id })
      .then((response) => dispatch(openImage(response)));
  };

  const onDelete = (ids: number[]) => {
    dispatch(deleteLocalImages(ids));
  };

  const onArchive = ({ id, archived }: LocalImage) => {
    if (archived) {
      dispatch(unarchive(id));
      window.electronPrisma.updateLocalImages({ where: { id }, data: { archived: false } });
    } else {
      dispatch(archive(id));
      window.electronPrisma.updateLocalImages({ where: { id }, data: { archived: true } });
    }
  };

  const onSelect = (id: number) => {
    if (selectedIds.includes(id)) {
      window.electronPrisma
        .updateLocalImages({ where: { id }, data: { selected: false } })
        .then(() => dispatch(unselect(id)));
    } else {
      window.electronPrisma
        .updateLocalImages({ where: { id }, data: { selected: true } })
        .then(() => dispatch(select(id)));
    }
  };

  const setPage = (page: number, options: { focusIndex?: number } = {}) => {
    const { focusIndex = 0 } = options;

    if (page > 0 && page <= pages) {
      dispatch(fetchLocalImages(page)).then(() => setFocus(focusIndex));
    }
  };

  useHotkeys(
    !localImage
      ? [
          ["PageDown", () => setPage(currentPage + 1)],
          ["PageUp", () => setPage(currentPage - 1)],
          ["mod+Delete", () => selectedIds.length && onDelete(selectedIds)],
        ]
      : []
  );

  const getFooter = useCallback(() => {
    console.log("heloo there");
    return LocalGalleryFooter({ onDelete, onPageChange: setPage });
  }, []);

  return (
    <PageLayout footer={getFooter()}>
      <ImageGrid>
        {localImages.map((image, index) => (
          <ImageCard
            ref={(card) => setRef(card, index)}
            key={index}
            image={image}
            selected={selectedIds.includes(image.id)}
            onSelect={onSelect}
            onDelete={onDelete}
            onArchive={onArchive}
            onOpenImage={onOpenImage}
            hotkeys={getImageCardHotkeys({ index, setPage })}
          />
        ))}
      </ImageGrid>
      <LocalGallerySyncModal />
      <LocalImageModal onSelect={onSelect} onDelete={onDelete} onArchive={onArchive} />
      <LoaderModal isLoading={fetch.status === FetchStatus.LOADING} />
    </PageLayout>
  );
}
